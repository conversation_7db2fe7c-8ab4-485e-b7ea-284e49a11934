// app.js

// Global variables
let lastOptimizeClickTime = 0;
const debounceTime = 1000; // 1 second debounce time

let lastImageBuffer = null;
let lastImageTimestamp = 0; // Add timestamp tracking for images
let windowPosition = { x: 50, y: 50 };
let windowSize = { width: 1150, height: 920 };
const moveStep = 30; // Increased from 10 to 20 for faster movement
const resizeStep = 30;
const zoomStep = 20;
const scrollStep = 100;
let transcription = '';
const MAX_MEMORY_SIZE = 10; // Limit memory to 10 previous prompts
let assistantConversationHistory = []; // Conversation history for Assistant tab
let systemDesignConversationHistory = []; // Conversation history for System Design tab
let generalChatConversationHistory = []; // Conversation history for General Chat tab

function appLog(message) {
  console.log(`[APP] ${message}`);
}

function isModifierKey(event) {
  if (!window.ui || typeof window.ui.isMac === 'undefined') {
    appLog("Error: window.ui.isMac not available, defaulting to Ctrl");
    return event.ctrlKey;
  }
  return window.ui.isMac ? event.metaKey : event.ctrlKey;
}

function toggleRecordingButtons(isRecording) {
  const startBtn = document.getElementById('startRecordingBtn');
  const stopBtn = document.getElementById('stopRecordingBtn');
  if (startBtn && stopBtn) {
    startBtn.disabled = isRecording;
    stopBtn.disabled = !isRecording;
    const statusElement = document.getElementById('status');
    const recordingStatusElement = document.getElementById('recording-status');
    if (statusElement) statusElement.textContent = isRecording ? 'Recording...' : 'Ready';
    if (recordingStatusElement) recordingStatusElement.textContent = isRecording ? 'Recording...' : 'Ready';
  } else {
    appLog("Error: Recording buttons not found");
  }
}

// Function to optimize code in the current chat
function optimizeCode() {
  appLog("Optimizing code from current chat");

  // Get the current active tab content
  const activeTab = document.querySelector('.tab.active');
  if (!activeTab) {
    appLog("No active tab found");
    return;
  }

  // Only proceed if we're in the assistant tab
  const tabId = activeTab.getAttribute('data-tab');
  if (tabId !== 'assistant') {
    if (window.ui && window.ui.showNotification) {
      window.ui.showNotification('Optimization is only available in the Assistant tab', 'warning');
    }
    appLog("Cannot optimize: not in assistant tab");
    return;
  }

  // Get the chat messages from the current conversation
  const chatMessages = document.getElementById('chat-messages');
  if (!chatMessages || !chatMessages.children.length) {
    if (window.ui && window.ui.showNotification) {
      window.ui.showNotification('No code to optimize. Please send a message first.', 'warning');
    }
    appLog("No chat messages to optimize");
    return;
  }

  // Get the last code snippet from the conversation
  const lastUserMessage = assistantConversationHistory.length > 0 ?
    assistantConversationHistory.filter(msg => msg.role === 'user').pop() : null;

  if (!lastUserMessage) {
    if (window.ui && window.ui.showNotification) {
      window.ui.showNotification('No previous messages to optimize.', 'warning');
    }
    appLog("No user messages in conversation history");
    return;
  }

  // Create an optimization prompt
  const language = getSelectedLanguage();
  const model = getSelectedModel();
  const optimizationPrompt = `
OPTIMIZATION REQUIREMENTS:
Please optimize the code from my previous message. Focus on:
1. Time complexity improvements
2. Space complexity optimizations
3. Better algorithm selection
4. Code readability enhancements
5. Think of all possible test cases, avoid Time Limit exceeded errors
6. iterate the solution obtained atleast 3 times making sure it covers all possible test cases.
7. Performance best practices for ${language}

Provide a detailed explanation of the optimizations made and why they improve the solution.
`;

  // Send the optimization prompt
  if (window.ui && window.ui.addChatMessage) {
    window.ui.addChatMessage('chat-messages', optimizationPrompt, true);
  }

  // Add to conversation history
  assistantConversationHistory.push({ role: 'user', content: optimizationPrompt });
  limitHistory(assistantConversationHistory);

  // Get status element
  const chatStatus = document.getElementById('chat-status');
  if (chatStatus) chatStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Optimizing...';

  // Process the optimization request
  processUserMessage('chat-messages', assistantConversationHistory, optimizationPrompt, model, language, false);
}

// Utility function to limit history size (modifies the array in place)
function limitHistory(history) {
  if (history.length > MAX_MEMORY_SIZE * 2) { // *2 because each prompt has user and assistant response
    history.splice(0, history.length - MAX_MEMORY_SIZE * 2); // Remove oldest entries
  }
  return history; // Return the modified array for chaining if needed
}

// Utility function to initialize custom dropdowns
function initializeCustomDropdowns() {
  appLog("Initializing custom dropdowns");

  // Store references to original select elements to maintain compatibility
  const languageSelect = document.createElement('select');
  languageSelect.id = 'language-select';
  languageSelect.style.display = 'none';
  document.body.appendChild(languageSelect);

  const modelSelect = document.createElement('select');
  modelSelect.id = 'model-select';
  modelSelect.style.display = 'none';
  document.body.appendChild(modelSelect);

  const systemDesignModelSelect = document.createElement('select');
  systemDesignModelSelect.id = 'system-design-model-select';
  systemDesignModelSelect.style.display = 'none';
  document.body.appendChild(systemDesignModelSelect);

  // Initialize options in the hidden selects
  const languages = ['Python', 'JavaScript', 'Java', 'C++'];
  const models = ['gpt-4o', 'deepseekR1','o3-mini'];

  languages.forEach(lang => {
    const option = document.createElement('option');
    option.value = lang;
    option.textContent = lang;
    languageSelect.appendChild(option);
  });

  models.forEach(model => {
    const option1 = document.createElement('option');
    option1.value = model;
    option1.textContent = model;
    modelSelect.appendChild(option1);

    const option2 = document.createElement('option');
    option2.value = model;
    option2.textContent = model;
    systemDesignModelSelect.appendChild(option2);
  });

  // Set initial values
  languageSelect.value = 'Python';
  modelSelect.value = 'gpt-4o';
  systemDesignModelSelect.value = 'gpt-4o';

  const dropdowns = document.querySelectorAll('.custom-dropdown');

  dropdowns.forEach(dropdown => {
    const selected = dropdown.querySelector('.dropdown-selected');
    const options = dropdown.querySelector('.dropdown-options');
    const optionElements = dropdown.querySelectorAll('.dropdown-option');

    // Open dropdown when clicking on selected element
    selected.addEventListener('click', (event) => {
      // Stop propagation to prevent document click from immediately closing it
      event.stopPropagation();

      // Close all other dropdowns first
      dropdowns.forEach(otherDropdown => {
        if (otherDropdown !== dropdown) {
          otherDropdown.classList.remove('open');
        }
      });

      // Toggle current dropdown
      dropdown.classList.toggle('open');

      // If opening, position the dropdown options correctly
      if (dropdown.classList.contains('open')) {
        const selectedRect = selected.getBoundingClientRect();
        const options = dropdown.querySelector('.dropdown-options');

        // Position the options directly under the dropdown
        options.style.position = 'absolute';
        options.style.top = '100%';
        options.style.left = '0';
        options.style.right = '0';
        options.style.width = '100%';

        // Ensure the options are above all other UI elements by setting a very high z-index
        // This is crucial for preventing the dropdown from being hidden behind other elements like "Ready"
        options.style.zIndex = '9999999';

        // Add a more visible border to help distinguish the dropdown options
        options.style.border = '2px solid var(--primary-color)';

        // Reset any previously set positioning styles
        options.style.bottom = 'auto';

        // Create a temporary container for the options if needed
        document.body.appendChild(options.cloneNode(true));
      }
    });

    // Handle option selection
    optionElements.forEach(option => {
      option.addEventListener('click', (event) => {
        // Stop propagation to prevent document click handler from triggering
        event.stopPropagation();

        const value = option.getAttribute('data-value');
        selected.textContent = value;

        // Update selected class
        optionElements.forEach(opt => opt.classList.remove('selected'));
        option.classList.add('selected');

        // Close dropdown
        dropdown.classList.remove('open');

        // Update the corresponding hidden select element
        if (dropdown.id === 'language-dropdown') {
          languageSelect.value = value;
        } else if (dropdown.id === 'model-dropdown') {
          modelSelect.value = value;
        } else if (dropdown.id === 'system-design-model-dropdown') {
          systemDesignModelSelect.value = value;
        }
      });
    });
  });

  // Close dropdowns when clicking outside
  document.addEventListener('click', (event) => {
    if (!event.target.closest('.custom-dropdown')) {
      dropdowns.forEach(dropdown => dropdown.classList.remove('open'));
    }
  });

  // Set initial selection
  dropdowns.forEach(dropdown => {
    const selected = dropdown.querySelector('.dropdown-selected');
    const options = dropdown.querySelectorAll('.dropdown-option');
    const initialValue = selected.textContent.trim();

    options.forEach(option => {
      if (option.getAttribute('data-value') === initialValue) {
        option.classList.add('selected');
      }
    });
  });
}

function getSelectedLanguage() {
  // Try new radio button UI first
  const selectedRadio = document.querySelector('#language-options input[type="radio"]:checked');
  if (selectedRadio) {
    const language = selectedRadio.value;
    console.log(`%c[LANGUAGE DEBUG] getSelectedLanguage: Radio button selection: "${language}"`, 'background: #4caf50; color: #fff; padding: 3px; border-radius: 3px;');
    return language;
  }
  // Fall back to legacy dropdown
  const languageSelect = document.getElementById('language-select');
  const language = languageSelect ? languageSelect.value : 'Python';
  console.log(`%c[LANGUAGE DEBUG] getSelectedLanguage: Dropdown fallback: "${language}"`, 'background: #ff9800; color: #fff; padding: 3px; border-radius: 3px;');
  return language;
}

function getSelectedModel(tabType = false) {
  // Handle different tab types
  let modelOptionsId;
  let fallbackSelectId;

  if (tabType === true || tabType === 'system-design') {
    // System Design tab
    modelOptionsId = 'system-design-model-options';
    fallbackSelectId = 'system-design-model-select';
  } else if (tabType === 'general-model') {
    // General Chat tab
    modelOptionsId = 'general-model-options';
    fallbackSelectId = 'general-model-select';
  } else {
    // Assistant tab (default)
    modelOptionsId = 'model-options';
    fallbackSelectId = 'model-select';
  }

  // Try new radio button UI first
  const selectedRadio = document.querySelector(`#${modelOptionsId} input[type="radio"]:checked`);
  if (selectedRadio) {
    return selectedRadio.value;
  }

  // Fall back to legacy dropdown
  const modelSelect = document.getElementById(fallbackSelectId);
  return modelSelect ? modelSelect.value : 'gpt-4o';
}

function getSelectedChatType() {
  // Get selected chat type for General Chat tab
  const selectedRadio = document.querySelector('#general-chat-type-options input[type="radio"]:checked');
  return selectedRadio ? selectedRadio.value : 'general';
}

// Assistant tab: Send message
function sendAssistantMessage() {
  const inputElement = document.getElementById('chat-input');
  if (!inputElement) {
    appLog("Error: #chat-input not found");
    return;
  }

  const message = inputElement.value.trim();
  const language = getSelectedLanguage();
  const model = getSelectedModel();

  if (message) {
    if (window.ui && window.ui.addChatMessage) {
      // Preserve the raw message exactly as entered by the user
      window.ui.addChatMessage('chat-messages', message, true);
    } else {
      appLog("Error: window.ui.addChatMessage not available");
      return;
    }

    // Store raw user message in history
    assistantConversationHistory.push({ role: 'user', content: message });
    limitHistory(assistantConversationHistory); // Limit history (modifies in place)
    appLog(`Assistant conversation history after adding user message: ${JSON.stringify(assistantConversationHistory)}`);
    inputElement.value = '';
    const chatStatus = document.getElementById('chat-status');
    if (chatStatus) chatStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Thinking...';

    // Send raw message as prompt; server handles language and model
    processUserMessage('chat-messages', assistantConversationHistory, message, model, language, false);
  }
}

// System Design tab: Send message
function sendSystemDesignMessage() {
  const inputElement = document.getElementById('system-design-input');
  if (!inputElement) {
    appLog("Error: #system-design-input not found");
    return;
  }

  const message = inputElement.value.trim();
  const model = getSelectedModel(true);

  if (message) {
    const systemDesignPrompt = `
    You are an expert in system design. For the following system design problem: "${message}", follow these steps:

    1. **Provide a Diagram in Mermaid Syntax**:
       - Create a detailed diagram to visualize the architecture (e.g., sequence diagram, component diagram, or architecture diagram).
       - Use a code block with the "mermaid" language identifier, like this:
         \`\`\`mermaid
         graph TD
         A[Client] --> B[Load Balancer]
         B --> C[Application Server]
         \`\`\`
       - **Mermaid Syntax Requirements**:
         - Use only the following arrow types: --> for a standard arrow, --- for no arrow, -.- for a dotted line, or ==> for a thick arrow.
         - Do not use unsupported arrow types like arrow_open, arrow_point, or any other custom arrow types.
         - Ensure all labels are enclosed in |label| format, e.g., -->|HTTP Request|.
         - Use subgraphs to group related components (e.g., frontend, backend, database, external services) for clarity.
         - Add colors to nodes and edges using Mermaid's styling syntax to enhance visual clarity. Ensure text is readable on all nodes by setting appropriate fill colors for text.
         - **Color Guidelines**:
           - Use the following color scheme for nodes:
             - Frontend nodes (e.g., Client): fill:#4a90e2 (a vibrant blue), text: white.
             - Backend nodes (e.g., Load Balancer, Application Server, Web Server): fill:#50c878 (a bright green), text: white.
             - Database nodes: fill:#e57373 (a deeper red), text: white.
             - External services (e.g., Authentication Service, Third-Party APIs): fill:#f4a261 (a warm orange), text: white.
           - Use the following colors for edges:
             - HTTP/HTTPS connections: stroke:#ff6f61 (coral red), stroke-width:2px.
             - Database queries: stroke:#4dd0e1 (cyan), stroke-width:2px.
             - API calls or external service interactions: stroke:#ffd700 (gold), stroke-width:2px.
         - Example with updated colors:
           \`\`\`mermaid
           graph TD
           subgraph Frontend
             A[Client]
           end
           subgraph Backend
             B[Load Balancer] -->|HTTP/HTTPS| C[Web Server]
             C -->|API Calls| D[Application Server]
           end
           subgraph Database
             E[Database]
           end
           subgraph External_Services
             F[Authentication Service]
             G[Third-Party APIs]
           end
           A --> B
           D -->|Database Query| E
           D -->|Auth Check| F
           D -->|External Data| G
           classDef frontend fill:#4a90e2,stroke:#333,stroke-width:2px,color:#ffffff;
           classDef backend fill:#50c878,stroke:#333,stroke-width:2px,color:#ffffff;
           classDef database fill:#e57373,stroke:#333,stroke-width:2px,color:#ffffff;
           classDef external fill:#f4a261,stroke:#333,stroke-width:2px,color:#ffffff;
           class A frontend;
           class B,C,D backend;
           class E database;
           class F,G external;
           linkStyle 0 stroke:#ff6f61,stroke-width:2px;
           linkStyle 1 stroke:#ffd700,stroke-width:2px;
           linkStyle 2 stroke:#4dd0e1,stroke-width:2px;
           linkStyle 3 stroke:#ffd700,stroke-width:2px;
           \`\`\`

    2. **Provide a Detailed Explanation**:
       - Include the following sections:
         1. A step-by-step breakdown of the system architecture.
         2. Key components and their interactions.
         3. Scalability, reliability, and performance considerations.
       - Ensure the explanation is clear, concise, and tailored to the problem.

    Example response format:
    \`\`\`mermaid
    graph TD
    subgraph Frontend
      A[Client]
    end
    subgraph Backend
      B[Load Balancer] -->|HTTP/HTTPS| C[Web Server]
      C -->|API Calls| D[Application Server]
    end
    subgraph Database
      E[Database]
    end
    subgraph External_Services
      F[Authentication Service]
      G[Third-Party APIs]
    end
    A --> B
    D -->|Database Query| E
    D -->|Auth Check| F
    D -->|External Data| G
    classDef frontend fill:#4a90e2,stroke:#333,stroke-width:2px,color:#ffffff;
    classDef backend fill:#50c878,stroke:#333,stroke-width:2px,color:#ffffff;
    classDef database fill:#e57373,stroke:#333,stroke-width:2px,color:#ffffff;
    classDef external fill:#f4a261,stroke:#333,stroke-width:2px,color:#ffffff;
    class A frontend;
    class B,C,D backend;
    class E database;
    class F,G external;
    linkStyle 0 stroke:#ff6f61,stroke-width:2px;
    linkStyle 1 stroke:#ffd700,stroke-width:2px;
    linkStyle 2 stroke:#4dd0e1,stroke-width:2px;
    linkStyle 3 stroke:#ffd700,stroke-width:2px;
    \`\`\`

    ### Explanation
    #### Step-by-Step Breakdown
    1. The client sends an HTTP/HTTPS request to the load balancer.
    2. The load balancer distributes the request to a web server.
    3. The web server forwards the request to an application server via API calls.
    4. The application server queries the database for data.
    5. The application server performs an authentication check with the authentication service.
    6. The application server fetches external data from third-party APIs.
    7. The response is sent back through the web server and load balancer to the client.

    #### Key Components and Interactions
    - **Client**: Initiates requests to the system.
    - **Load Balancer**: Distributes incoming traffic across multiple web servers.
    - **Web Server**: Handles initial request processing and forwards to the application server.
    - **Application Server**: Manages business logic, queries the database, and interacts with external services.
    - **Database**: Stores and retrieves data as requested.
    - **Authentication Service**: Validates user credentials.
    - **Third-Party APIs**: Provide external data or functionality.

    #### Scalability, Reliability, and Performance Considerations
    - **Scalability**: The load balancer enables horizontal scaling by adding more web and application servers. The database can be sharded or replicated to handle increased load.
    - **Reliability**: Use redundant load balancers, web servers, and application servers to avoid single points of failure. Implement database replication for fault tolerance.
    - **Performance**: Optimize database queries and use caching (e.g., Redis) to reduce load on the database. Ensure the load balancer uses efficient routing algorithms. Minimize latency in external API calls by caching responses where possible.
    `;
    if (window.ui && window.ui.addChatMessage) {
      window.ui.addChatMessage('system-design-messages', message, true);
    } else {
      appLog("Error: window.ui.addChatMessage not available");
      return;
    }
    // Store only the raw user message in history
    systemDesignConversationHistory.push({ role: 'user', content: message });
    limitHistory(systemDesignConversationHistory); // Limit history (modifies in place)
    appLog(`System design conversation history after adding user message: ${JSON.stringify(systemDesignConversationHistory)}`);
    inputElement.value = '';
    const systemDesignStatus = document.getElementById('system-design-status');
    if (systemDesignStatus) systemDesignStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Thinking...';
    // Language is not relevant for system design, so pass null
    processUserMessage('system-design-messages', systemDesignConversationHistory, systemDesignPrompt, model, null, true);
  }
}

// General Chat tab: Send message
function sendGeneralChatMessage() {
  const inputElement = document.getElementById('general-chat-input');
  if (!inputElement) {
    appLog("Error: #general-chat-input not found");
    return;
  }

  const message = inputElement.value.trim();
  const model = getSelectedModel('general-model');
  const chatType = getSelectedChatType();

  if (message) {
    if (window.ui && window.ui.addChatMessage) {
      window.ui.addChatMessage('general-chat-messages', message, true);
    } else {
      appLog("Error: window.ui.addChatMessage not available");
      return;
    }

    // Create context-aware prompt based on chat type
    let enhancedPrompt = message;

    switch (chatType) {
      case 'quiz':
        enhancedPrompt = `Mental Quiz/Brain Teaser: ${message}

Please provide a thoughtful response that includes:
- Clear explanation of the solution
- Step-by-step reasoning
- Alternative approaches if applicable
- Tips for similar problems

Keep it engaging and educational!`;
        break;

      case 'trivia':
        enhancedPrompt = `Trivia Question: ${message}

Please provide:
- The answer with explanation
- Interesting background information
- Related fun facts
- Context about why this is significant

Make it informative and entertaining!`;
        break;

      case 'casual':
        enhancedPrompt = `Casual Chat: ${message}

Please respond in a friendly, conversational tone. Feel free to be:
- Relaxed and natural
- Engaging and personable
- Helpful but not overly formal
- Open to follow-up questions`;
        break;

      default: // general
        enhancedPrompt = `General Question: ${message}

Please provide a helpful, comprehensive response that:
- Addresses the question directly
- Provides relevant context
- Offers practical insights
- Is clear and easy to understand`;
        break;
    }

    // Store the raw user message in history
    generalChatConversationHistory.push({ role: 'user', content: message });
    limitHistory(generalChatConversationHistory);
    appLog(`General chat conversation history after adding user message: ${JSON.stringify(generalChatConversationHistory)}`);
    inputElement.value = '';
    const generalChatStatus = document.getElementById('general-chat-status');
    if (generalChatStatus) generalChatStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Thinking...';

    // Process the message (no language needed for general chat)
    processUserMessage('general-chat-messages', generalChatConversationHistory, enhancedPrompt, model, null, false);
  }
}

async function processUserMessage(containerId, conversationHistory, message, model, language, isSystemDesign) {
  try {
    // First try to get credentials from helperCredentials in localStorage
    let credentials = JSON.parse(localStorage.getItem('helperCredentials'));

    // If credentials are missing, try to get from the user object as a fallback
    if (!credentials || !credentials.email || !credentials.secretKey) {
      const user = JSON.parse(localStorage.getItem('user'));
      if (user && user.email && user.secretKey) {
        credentials = {
          email: user.email,
          secretKey: user.secretKey
        };
        // Store these credentials for future use
        localStorage.setItem('helperCredentials', JSON.stringify(credentials));
        appLog("Retrieved credentials from user object and stored in helperCredentials");
      } else {
        throw new Error('Missing credentials. Please sign in again from the dashboard.');
      }
    }

    console.log(language);

    // Enhance the prompt with language instructions and code generation rules
    let enhancedMessage = message;
    if (!isSystemDesign && language) {
      // Add strict language instruction at the beginning of the message with stronger emphasis
      enhancedMessage = `CRITICAL INSTRUCTION: YOU MUST ONLY PROVIDE CODE IN ${language.toUpperCase()}.
DO NOT PROVIDE ANY CODE IN OTHER LANGUAGES UNDER ANY CIRCUMSTANCES.

${message}

STRICT SOLUTION REQUIREMENTS:
1. For coding questions, you MUST ONLY use ${language} as the programming language.
2. If the question is not about programming, do not generate any code.
3. When providing code solutions:
   - Answer like how you would answer the interviewer in human way.
   - explain your approach and reasoning
   - You may provide a brute force solution if applicable
   - Then provide optimized approaches
   - Show time and space complexity
4. NEVER switch to another language even for comparison
5. Remember: ONLY ${language} CODE IS ALLOWED in this response

Response language: ${language}`;
    }

    // Check if this is an optimization request and further enhance the prompt
    if (!isSystemDesign && message.includes('OPTIMIZATION REQUIREMENTS')) {
      // This is an optimization request, add special instructions for iterative improvement
      enhancedMessage = `${enhancedMessage}

ADDITIONAL OPTIMIZATION GUIDELINES:
- Think step by step and explore multiple approaches
- Consider all possible algorithmic improvements
- Identify and eliminate any unnecessary operations
- Optimize memory usage and reduce allocations
- Consider amortized complexity when relevant
- Make the code as efficient as possible for production use`;
    }

    appLog(`Sending request to server with prompt: ${enhancedMessage}, model: ${model}, language: ${language}`);
    limitHistory(conversationHistory); // Limit history before sending (modifies in place)
    appLog(`Conversation history being sent: ${JSON.stringify(conversationHistory)}`);

    // Use the new electronAPI.sendChatMessage instead of direct fetch
    if (!window.electronAPI || !window.electronAPI.sendChatMessage) {
      throw new Error('Chat API is not available. Please restart the application.');
    }

    const result = await window.electronAPI.sendChatMessage(
      conversationHistory,
      enhancedMessage,
      model,
      language
    );

    // Handle errors from the chat API
    if (result.error) {
      throw new Error(result.error);
    }

    // Check for success flag in the response
    if (!result.success) {
      throw new Error('Chat request failed: No success flag in response');
    }

    appLog(`Received response from server successfully`);
    let aiResponse = result.response;
    if (!aiResponse) {
      throw new Error('No response received from server');
    }

    conversationHistory.push({ role: 'assistant', content: aiResponse });
    limitHistory(conversationHistory); // Limit history after response (modifies in place)
    appLog(`Updated conversation history: ${JSON.stringify(conversationHistory)}`);

    if (window.ui && window.ui.addChatMessage) {
      window.ui.addChatMessage(containerId, aiResponse, false);
    }

    const statusElement = document.getElementById(isSystemDesign ? 'system-design-status' : 'chat-status');
    if (statusElement) statusElement.textContent = 'Ready';
  } catch (error) {
    console.error("Error processing message:", error);
    const statusElement = document.getElementById(isSystemDesign ? 'system-design-status' : 'chat-status');
    if (statusElement) statusElement.textContent = 'Error';
    if (window.ui && window.ui.addChatMessage) {
      window.ui.addChatMessage(containerId, `Sorry, there was an error: ${error.message}`, false);
    }
  }
}

function scrollActiveTab(direction) {
  const activeTab = document.querySelector('.tab.active');
  if (!activeTab) return;
  const tabId = activeTab.getAttribute('data-tab');
  let scrollElement;
  if (tabId === 'assistant') {
    scrollElement = document.getElementById('chat-messages');
  } else if (tabId === 'system-design') {
    scrollElement = document.getElementById('system-design-messages');
  } else if (tabId === 'audio') {
    scrollElement = document.getElementById('recording-info');
  } else if (tabId === 'shortcuts') {
    scrollElement = document.getElementById('shortcuts-tab');
  }
  if (scrollElement) {
    const scrollAmount = direction === 'up' ? -scrollStep : scrollStep;
    scrollElement.scrollBy({ top: scrollAmount, behavior: 'smooth' });
    appLog(`Scrolling ${tabId} tab by ${scrollAmount}px`);
  }
}

async function moveWindow(deltaX, deltaY) {
  try {
    if (!window.electronAPI) {
      appLog("Error: window.electronAPI not available for moveWindow");
      return false;
  }
const position = await window.electronAPI.getWindowPosition();
    if (position && typeof position.x === 'number' && typeof position.y === 'number') windowPosition = position;
    const newX = windowPosition.x + deltaX;
    const newY = windowPosition.y + deltaY;
    appLog(`Moving window from (${windowPosition.x}, ${windowPosition.y}) to (${newX}, ${newY})`);
    const result = await window.electronAPI.moveWindow(newX, newY);
    if (result) {
      windowPosition.x = newX;
      windowPosition.y = newY;
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error moving window:", error);
    return false;
  }
}

async function resizeWindow(width, height) {
  try {
    if (!window.electronAPI) {
      appLog("Error: window.electronAPI not available for resizeWindow");
      return false;
    }
    if (typeof width !== 'number' || typeof height !== 'number') {
      appLog("Invalid resize dimensions");
      return false;
    }
    appLog(`Resizing window to ${width} × ${height}`);
    const result = await window.electronAPI.resizeWindow(width, height);
    if (result) {
      windowSize.width = result.width;
      windowSize.height = result.height;
  if (window.ui && window.ui.updateSizeDisplay) {
    window.ui.updateSizeDisplay(windowSize.width, windowSize.height);
      }
      return true;
  }
return false;
  } catch (error) {
    console.error("Error resizing window:", error);
    return false;
  }
}

async function saveResults() {
  const activeTab = document.querySelector('.tab.active');
  if (!activeTab) return;
  const tabId = activeTab.getAttribute('data-tab');
  let content;
  if (tabId === 'assistant') {
    const chatMessages = document.getElementById('chat-messages');
    if (!chatMessages) return;
    content = chatMessages.innerText;
  } else if (tabId === 'system-design') {
    const systemDesignMessages = document.getElementById('system-design-messages');
    if (!systemDesignMessages) return;
    content = systemDesignMessages.innerText;
  } else if (tabId === 'general') {
    const generalChatMessages = document.getElementById('general-chat-messages');
    if (!generalChatMessages) return;
    content = generalChatMessages.innerText;
  }
  if (!content || content.trim() === '') {
    alert('No content to save!');
    return;
  }
  try {
    if (window.electronAPI) {
      const saved = await window.electronAPI.saveToFile(content);
      const statusElement = document.getElementById('status');
      if (statusElement) statusElement.textContent = saved ? 'Results saved!' : 'Failed to save results';
    } else {
  const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${tabId}-chat-${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.txt`;
      a.click();
      URL.revokeObjectURL(url);
      const statusElement = document.getElementById('status');
      if (statusElement) statusElement.textContent = 'Results downloaded!';
    }
  } catch (error) {
    console.error("Error saving results:", error);
    const statusElement = document.getElementById('status');
    if (statusElement) statusElement.textContent = 'Error saving results: ' + error.message;
  }
}

// Setup event listeners
function setupEventListeners() {
  appLog("Setting up event listeners");
  const elements = {
    closeBtn: document.getElementById('closeBtn'),
    chatInput: document.getElementById('chat-input'),
    sendButton: document.getElementById('send-button'),
    clearChat: document.getElementById('clear-chat'),
    optimizeCode: document.getElementById('optimize-code'),
    systemDesignInput: document.getElementById('system-design-input'),
    systemDesignSendButton: document.getElementById('system-design-send-button'),
    systemDesignClearChat: document.getElementById('system-design-clear-chat'),
    systemDesignSaveChat: document.getElementById('system-design-save-chat'),
    generalChatInput: document.getElementById('general-chat-input'),
    generalSendButton: document.getElementById('general-send-button'),
    generalClearChat: document.getElementById('general-clear-chat'),
    generalCaptureBtn: document.getElementById('general-capture-btn'),
    generalProcessBtn: document.getElementById('general-process-btn'),
    generalSaveChat: document.getElementById('general-save-chat'),
    startRecordingBtn: document.getElementById('startRecordingBtn'),
    stopRecordingBtn: document.getElementById('stopRecordingBtn'),
  };

  for (const [key, element] of Object.entries(elements)) {
    if (!element) appLog(`Error: #${key} not found in DOM`);
  }

  if (elements.closeBtn) {
    elements.closeBtn.addEventListener('click', async () => {
      try {
        if (window.electronAPI) {
          await window.electronAPI.closeWindow();
        } else {
          appLog("Error: window.electronAPI not available for closeWindow");
        }
      } catch (error) {
        console.error("Error closing window:", error);
      }
    });
  }

  if (elements.chatInput) {
    elements.chatInput.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendAssistantMessage();
      }
    });
  }

  if (elements.sendButton) {
    elements.sendButton.addEventListener('click', sendAssistantMessage);
  }

  if (elements.clearChat) {
    elements.clearChat.addEventListener('click', () => {
      if (window.ui && window.ui.clearChat) {
        window.ui.clearChat('chat-messages');
        assistantConversationHistory = [];
      } else {
        appLog("Error: window.ui.clearChat not available");
      }
    });
  }

  if (elements.optimizeCode) {
    elements.optimizeCode.addEventListener('click', (event) => {
      // Prevent any default behavior
      event.preventDefault();
      // Stop any potential event propagation
      event.stopPropagation();

      const currentTime = Date.now();
      if (currentTime - lastOptimizeClickTime < debounceTime) {
        appLog("Optimize code button click ignored due to debounce");
        return;
      }
      lastOptimizeClickTime = currentTime;

      appLog("Optimize code button clicked");
      // Direct function invocation to avoid duplicate calls
      optimizeCode();
    });
  }

  if (elements.systemDesignInput) {
    elements.systemDesignInput.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendSystemDesignMessage();
      }
    });
  }

  if (elements.systemDesignSendButton) {
    elements.systemDesignSendButton.addEventListener('click', sendSystemDesignMessage);
  }

  if (elements.systemDesignClearChat) {
    elements.systemDesignClearChat.addEventListener('click', () => {
      if (window.ui && window.ui.clearChat) {
        window.ui.clearChat('system-design-messages');
        systemDesignConversationHistory = [];
      } else {
        appLog("Error: window.ui.clearChat not available");
      }
    });
  }

  if (elements.systemDesignSaveChat) {
    elements.systemDesignSaveChat.addEventListener('click', () => {
      appLog("System Design save button clicked");
      saveResults();
    });
  } else {
    appLog("Error: #system-design-save-chat not found in DOM");
  }

  // General Chat event listeners
  if (elements.generalChatInput) {
    elements.generalChatInput.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendGeneralChatMessage();
      }
    });
  }

  if (elements.generalSendButton) {
    elements.generalSendButton.addEventListener('click', sendGeneralChatMessage);
  }

  if (elements.generalClearChat) {
    elements.generalClearChat.addEventListener('click', () => {
      if (window.ui && window.ui.clearChat) {
        window.ui.clearChat('general-chat-messages');
        generalChatConversationHistory = [];
      } else {
        appLog("Error: window.ui.clearChat not available");
      }
    });
  }

  if (elements.generalCaptureBtn) {
    elements.generalCaptureBtn.addEventListener('click', () => {
      if (window.electronAPI && window.electronAPI.triggerCapture) {
        window.electronAPI.triggerCapture();
        appLog("General Chat: Triggered capture");
      }
    });
  }

  if (elements.generalProcessBtn) {
    elements.generalProcessBtn.addEventListener('click', () => {
      if (window.electronAPI && window.electronAPI.triggerProcess) {
        window.electronAPI.triggerProcess();
        appLog("General Chat: Triggered process");
      }
    });
  }

  if (elements.generalSaveChat) {
    elements.generalSaveChat.addEventListener('click', () => {
      appLog("General Chat save button clicked");
      saveResults();
    });
  }

  if (elements.startRecordingBtn) {
    elements.startRecordingBtn.addEventListener('click', async () => {
      appLog("Start recording button clicked");
      transcription = '';
      try {
        if (window.electronAPI) {
          const success = await window.electronAPI.startRecording();
          if (success) {
            appLog("Recording started successfully");
            toggleRecordingButtons(true);
          } else {
            appLog("Failed to start recording");
            const statusElement = document.getElementById('status');
            const recordingStatusElement = document.getElementById('recording-status');
            if (statusElement) statusElement.textContent = 'Failed to start recording';
            if (recordingStatusElement) recordingStatusElement.textContent = 'Failed';
          }
        } else {
          appLog("Error: window.electronAPI not available for startRecording");
        }
      } catch (error) {
        console.error("Error starting recording:", error);
        const statusElement = document.getElementById('status');
        const recordingStatusElement = document.getElementById('recording-status');
        if (statusElement) statusElement.textContent = 'Recording error: ' + error.message;
        if (recordingStatusElement) recordingStatusElement.textContent = 'Error';
      }
    });
  }

  if (elements.stopRecordingBtn) {
    elements.stopRecordingBtn.addEventListener('click', async () => {
      appLog("Stop recording button clicked");
      try {
        if (window.electronAPI) {
          const result = await window.electronAPI.stopRecording();
          if (result.success) {
            appLog("Recording stopped successfully");
            toggleRecordingButtons(false);
          } else {
            appLog("Failed to stop recording");
            const statusElement = document.getElementById('status');
            const recordingStatusElement = document.getElementById('recording-status');
            if (statusElement) statusElement.textContent = 'Failed to stop recording';
            if (recordingStatusElement) recordingStatusElement.textContent = 'Failed';
          }
        } else {
          appLog("Error: window.electronAPI not available for stopRecording");
        }
      } catch (error) {
        console.error("Error stopping recording:", error);
        const statusElement = document.getElementById('status');
        const recordingStatusElement = document.getElementById('recording-status');
        if (statusElement) statusElement.textContent = 'Recording error: ' + error.message;
        if (recordingStatusElement) recordingStatusElement.textContent = 'Error';
      }
    });
  }

  if (window.electronAPI) {
    appLog("Electron API available");
    window.electronAPI.onCapture(async () => {
      appLog("Capture event received");
      const loadingOverlay = document.getElementById('loadingOverlay');
      const statusElement = document.getElementById('status');
      if (loadingOverlay) loadingOverlay.style.display = 'flex';
      if (statusElement) statusElement.textContent = 'Capturing...';
      try {
        lastImageBuffer = await window.electronAPI.captureScreen();
        lastImageTimestamp = Date.now(); // Update timestamp when image is captured
        appLog("captureScreen returned", lastImageBuffer ? `${lastImageBuffer.length} bytes` : "null");

        // Show notification that screenshot was taken
        if (lastImageBuffer && window.ui && window.ui.showNotification) {
          window.ui.showNotification('Screenshot captured successfully!', 'success', 3000);
        }

        if (statusElement) {
          statusElement.textContent = lastImageBuffer ? 'Captured! Ready to process.' : 'Capture failed';
        }
      } catch (error) {
        console.error("Error during capture:", error);
        if (statusElement) statusElement.textContent = 'Capture error: ' + error.message;
        if (window.ui && window.ui.showNotification) {
          window.ui.showNotification('Error capturing screenshot: ' + error.message, 'error');
        }
      } finally {
        if (loadingOverlay) loadingOverlay.style.display = 'none';
      }
    });

    window.electronAPI.onProcess(async () => {
      appLog("Process event received");

      // Check if we have an image to process
      if (!lastImageBuffer) {
        appLog("No image buffer available");
        const statusElement = document.getElementById('status');
        if (statusElement) statusElement.textContent = 'No image captured yet';
        if (window.ui && window.ui.showNotification) {
          window.ui.showNotification('Please capture a screenshot first', 'warning');
        }
        if (window.ui && window.ui.addChatMessage) {
          window.ui.addChatMessage('chat-messages', `Please capture a screen first (${window.ui.isMac ? 'Cmd+Shift+C' : 'Ctrl+Shift+C'})`, false);
        }
        return;
      }

      // Get UI elements and set up processing variables
      const loadingOverlay = document.getElementById('loadingOverlay');
      const statusElement = document.getElementById('status');
      const activeTab = document.querySelector('.tab.active');
      if (!activeTab) return;
      const tabId = activeTab.getAttribute('data-tab');

      let containerId, model, language, conversationHistory;

      if (tabId === 'assistant') {
        containerId = 'chat-messages';
        model = getSelectedModel();
        language = getSelectedLanguage();
        conversationHistory = assistantConversationHistory;
      } else if (tabId === 'system-design') {
        containerId = 'system-design-messages';
        model = getSelectedModel('system-design');
        language = null;
        conversationHistory = systemDesignConversationHistory;
      } else if (tabId === 'general') {
        containerId = 'general-chat-messages';
        model = getSelectedModel('general-model');
        language = null;
        conversationHistory = generalChatConversationHistory;
      } else {
        appLog(`Unknown tab: ${tabId}`);
        return;
      }

      console.log(`%c[LANGUAGE DEBUG] Image Processing: Using language "${language}" for screenshot analysis`,
                 'background: #e91e63; color: #fff; padding: 3px; border-radius: 3px;');

      const isSystemDesign = tabId === 'system-design';
      let statusDisplay;

      if (tabId === 'assistant') {
        statusDisplay = document.getElementById('chat-status');
      } else if (tabId === 'system-design') {
        statusDisplay = document.getElementById('system-design-status');
      } else if (tabId === 'general') {
        statusDisplay = document.getElementById('general-chat-status');
      }

      // Track this processing time to prevent duplicate processing
      const currentTime = Date.now();
      const processId = Math.floor(Math.random() * 1000000); // Generate a unique ID for this process
      appLog(`Starting process #${processId} at ${currentTime}, last image from ${lastImageTimestamp}`);

      // Show loading indicators
      if (loadingOverlay) loadingOverlay.style.display = 'flex';
      if (statusElement) statusElement.textContent = 'Processing...';
      if (statusDisplay) statusDisplay.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

      try {
        // Add the image to the chat
        const imageBase64 = lastImageBuffer.toString('base64');
        const imageDataUrl = `data:image/png;base64,${imageBase64}`;
        if (window.ui && window.ui.addChatImage) {
          window.ui.addChatImage(containerId, imageDataUrl, true);
        }

        // Step 1: Perform OCR using preload.js
        if (!window.electronAPI || !window.electronAPI.performOCR) {
          throw new Error('window.electronAPI.performOCR is not available');
        }

        const text = await window.electronAPI.performOCR(lastImageBuffer);
        appLog(`Process #${processId}: OCR result received`);

        if (text.startsWith('OCR error') || text === 'Missing credentials. Please sign in again.') {
          throw new Error(text);
        }

        // Step 2: Analyze with AI using preload.js
        if (!window.electronAPI || !window.electronAPI.analyzeWithAI) {
          throw new Error('window.electronAPI.analyzeWithAI is not available');
        }

        let prompt;
        if (isSystemDesign) {
          prompt = `
You are an expert in system design. Analyze the following screenshot content: "${text}". First, provide a diagram in Mermaid syntax to visualize the architecture (e.g., sequence diagram, component diagram, or architecture diagram). Use a code block with the "mermaid" language identifier. Then, provide a detailed system design explanation, including:
1. A step-by-step breakdown of the system architecture.
2. Key components and their interactions.
3. Scalability, reliability, and performance considerations.
`;
        } else if (tabId === 'general') {
          // For General Chat tab, create context-aware prompt based on chat type
          const chatType = getSelectedChatType();

          switch (chatType) {
            case 'quiz':
              prompt = `Mental Quiz/Brain Teaser Analysis: Analyze the following screenshot content: "${text}".

If this appears to be a mental quiz, brain teaser, or puzzle, please provide:
- Clear explanation of the solution
- Step-by-step reasoning
- Alternative approaches if applicable
- Tips for similar problems

Keep it engaging and educational!`;
              break;

            case 'trivia':
              prompt = `Trivia Analysis: Analyze the following screenshot content: "${text}".

If this appears to be a trivia question or general knowledge topic, please provide:
- The answer with explanation
- Interesting background information
- Related fun facts
- Context about why this is significant

Make it informative and entertaining!`;
              break;

            case 'casual':
              prompt = `Casual Analysis: Analyze the following screenshot content: "${text}".

Please respond in a friendly, conversational tone. Feel free to be:
- Relaxed and natural
- Engaging and personable
- Helpful but not overly formal
- Open to follow-up questions`;
              break;

            default: // general
              prompt = `General Analysis: Analyze the following screenshot content: "${text}".

Please provide a helpful, comprehensive response that:
- Addresses the content directly
- Provides relevant context
- Offers practical insights
- Is clear and easy to understand`;
              break;
          }
        } else {
          // For Assistant tab, add language instruction similar to chat messages
          if (language) {
            prompt = `CRITICAL INSTRUCTION: YOU MUST ONLY PROVIDE CODE IN ${language.toUpperCase()}.
DO NOT PROVIDE ANY CODE IN OTHER LANGUAGES UNDER ANY CIRCUMSTANCES.

Analyze this screenshot content: ${text}

STRICT SOLUTION REQUIREMENTS:
1. For coding questions, you MUST ONLY use ${language} as the programming language.
2. If the question is not about programming, do not generate any code.
3. When providing code solutions:
   - First explain your approach and reasoning
   - You may provide a brute force solution if applicable
   - Then provide optimized approaches
   - Show time and space complexity
4. NEVER switch to another language even for comparison
5. Remember: ONLY ${language} CODE IS ALLOWED in this response

Response language: ${language}`;
          } else {
            prompt = `Analyze this screenshot content: ${text}`;
          }
        }

        // This helps ensure we don't process the same image multiple times
        const now = Date.now();
        if (processId !== processId) {
          appLog(`Process #${processId} aborted: newer process has started`);
          return;
        }

        conversationHistory.push({ role: 'user', content: `Screenshot analysis: ${text}` });
        limitHistory(conversationHistory); // Limit history (modifies in place)
        appLog(`Process #${processId}: Conversation history updated, sending for analysis`);

        const analysis = await window.electronAPI.analyzeWithAI(prompt, conversationHistory, model, language);
        appLog(`Process #${processId}: AI analysis complete`);

        if (analysis.startsWith('AI analysis error') || analysis === 'Missing credentials. Please sign in again.') {
          throw new Error(analysis);
        }

        conversationHistory.push({ role: 'assistant', content: analysis });
        limitHistory(conversationHistory); // Limit history (modifies in place)

        if (window.ui && window.ui.addChatMessage) {
          window.ui.addChatMessage(containerId, analysis, false);
        }

        if (statusElement) statusElement.textContent = 'Done!';
        if (statusDisplay) statusDisplay.textContent = 'Ready';

        // Show success notification
        if (window.ui && window.ui.showNotification) {
          window.ui.showNotification('Screenshot processed successfully!', 'success');
        }

      } catch (error) {
        console.error(`Process #${processId} error:`, error);
        if (statusElement) statusElement.textContent = 'Processing error: ' + error.message;
        if (statusDisplay) statusDisplay.textContent = 'Error';
        if (window.ui && window.ui.addChatMessage) {
          window.ui.addChatMessage(containerId, 'Error: ' + error.message, false);
        }

        // Show error notification
        if (window.ui && window.ui.showNotification) {
          window.ui.showNotification('Error processing screenshot: ' + error.message, 'error');
        }
      } finally {
        if (loadingOverlay) loadingOverlay.style.display = 'none';
      }
    });

    window.electronAPI.onRecordingStarted(() => {
      appLog("Recording started event received");
      toggleRecordingButtons(true);
    });

    window.electronAPI.onRecordingStopped(() => {
      appLog("Recording stopped event received");
      toggleRecordingButtons(false);
    });

    window.electronAPI.onAudioChunk((chunkBase64) => {
      appLog("Received audio chunk: " + chunkBase64);
    });

    window.electronAPI.on('transcription-result', (event, data) => {
      appLog("Received transcription result via IPC");
      transcription += (data.transcription || '') + ' ';
      if (window.ui && window.ui.addChatMessage) {
        window.ui.addChatMessage('chat-messages', `Transcription: ${data.transcription || 'N/A'}`, false);
      }
      if (data.response) {
        if (window.ui && window.ui.addChatMessage) {
          window.ui.addChatMessage('chat-messages', `AI Response: ${data.response}`, false);
        }
      }
      const recordingInfo = document.getElementById('recording-info');
      if (recordingInfo) {
        recordingInfo.textContent = `Transcription: ${transcription.trim()}\n\nLatest Response: ${data.response || 'N/A (<10 words)'}`;
      }
    });
  } else {
    appLog("Error: ElectronAPI not available, running in development mode");
  }

  document.addEventListener('keydown', (event) => {
    if (isModifierKey(event) && event.shiftKey && !event.altKey) {
      appLog(`Keydown: ${window.ui.isMac ? 'Cmd' : 'Ctrl'}+Shift+${event.key}`);
      switch (event.key) {
        case 'C':
          // Use the new triggerCapture method instead of onCapture
          if (window.electronAPI && window.electronAPI.triggerCapture) {
            window.electronAPI.triggerCapture();
            appLog("Triggered debounced capture event");
          }
          event.preventDefault();
          break;
        case 'P':
          // Use the new triggerProcess method instead of onProcess
          if (window.electronAPI && window.electronAPI.triggerProcess) {
            window.electronAPI.triggerProcess();
            appLog("Triggered debounced process event");
          }
          event.preventDefault();
          break;
        case 'T':
          if (window.electronAPI) window.electronAPI.toggleWindow();
          event.preventDefault();
          break;
        case 'I':
          resizeWindow(windowSize.width, windowSize.height + resizeStep);
          event.preventDefault();
          break;
        case 'K':
          resizeWindow(windowSize.width, windowSize.height - resizeStep);
          event.preventDefault();
          break;
        case 'R':
          const startRecordingBtn = document.getElementById('startRecordingBtn');
          if (startRecordingBtn && startRecordingBtn.disabled) {
            if (window.electronAPI) window.electronAPI.stopRecording();
          } else {
            if (window.electronAPI) window.electronAPI.startRecording();
          }
          event.preventDefault();
          break;
        case 'ArrowUp':
          resizeWindow(windowSize.width, windowSize.height - resizeStep);
          event.preventDefault();
          break;
        case 'ArrowDown':
          resizeWindow(windowSize.width, windowSize.height + resizeStep);
          event.preventDefault();
          break;
        case 'ArrowLeft':
          resizeWindow(windowSize.width - resizeStep, windowSize.height);
          event.preventDefault();
          break;
        case 'ArrowRight':
          resizeWindow(windowSize.width + resizeStep, windowSize.height);
          event.preventDefault();
          break;
      }
    }
    if (isModifierKey(event) && !event.shiftKey && !event.altKey) {
      switch (event.key) {
        case 'ArrowUp':
          moveWindow(0, -moveStep);
          event.preventDefault();
          break;
        case 'ArrowDown':
          moveWindow(0, moveStep);
          event.preventDefault();
          break;
        case 'ArrowLeft':
          moveWindow(-moveStep, 0);
          event.preventDefault();
          break;
        case 'ArrowRight':
          moveWindow(moveStep, 0);
          event.preventDefault();
          break;
        case '+':
        case '=':
          resizeWindow(windowSize.width + zoomStep, windowSize.height + zoomStep);
          event.preventDefault();
          break;
        case '-':
          resizeWindow(windowSize.width - zoomStep, windowSize.height - zoomStep);
          event.preventDefault();
          break;
      }
    }
    if (isModifierKey(event) && event.altKey && !event.shiftKey) {
      switch (event.key) {
        case 'ArrowUp':
          scrollActiveTab('up');
          event.preventDefault();
          break;
        case 'ArrowDown':
          scrollActiveTab('down');
          event.preventDefault();
          break;
      }
    }
  });
}

// Focus management for preventing website focus detection
function initFocusManagement() {
  appLog("Initializing focus management");

  // Prevent the helper app from stealing focus from target websites
  window.addEventListener('focus', () => {
    appLog("Helper app gained focus - attempting to prevent focus steal");

    // Try to communicate with target websites that helper is active
    try {
      // Send message to all windows
      if (window.opener && !window.opener.closed) {
        window.opener.postMessage({
          type: 'interviewcracker-helper',
          active: true,
          source: 'helper-app'
        }, '*');
      }

      // Also try parent window
      if (window.parent && window.parent !== window) {
        window.parent.postMessage({
          type: 'interviewcracker-helper',
          active: true,
          source: 'helper-app'
        }, '*');
      }

      // Broadcast to all windows
      window.postMessage({
        type: 'interviewcracker-helper',
        active: true,
        source: 'helper-app'
      }, '*');

    } catch (error) {
      appLog("Could not communicate with target website: " + error.message);
    }

    // Try to blur the helper window to restore focus to the target website
    setTimeout(() => {
      try {
        if (window.electronAPI && window.electronAPI.blurWindow) {
          window.electronAPI.blurWindow();
        }
      } catch (error) {
        appLog("Could not blur helper window: " + error.message);
      }
    }, 100);
  });

  // Send periodic status updates
  setInterval(() => {
    try {
      window.postMessage({
        type: 'interviewcracker-helper',
        active: true,
        source: 'helper-app',
        timestamp: Date.now()
      }, '*');
    } catch (error) {
      // Ignore errors
    }
  }, 5000);

  // Listen for ping messages from target websites
  window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'interviewcracker-ping') {
      // Respond to ping
      try {
        window.postMessage({
          type: 'interviewcracker-pong',
          timestamp: Date.now(),
          source: 'helper-app'
        }, '*');
      } catch (error) {
        // Ignore errors
      }
    }
  });

  appLog("Focus management initialized");
}

// Focus-Safe Mode removed

function initFocusSafeMode() {
  appLog("Focus-Safe Mode initialized (indicator removed)");
  // Focus-safe mode functionality removed - no longer needed
}

// Focus-safe mode functions removed

function enableFocusSafeMode() {
  // Make all input fields read-only to prevent focus
  const inputs = document.querySelectorAll('input, textarea, [contenteditable]');
  inputs.forEach(input => {
    input.dataset.originalReadonly = input.readOnly || input.getAttribute('readonly') || 'false';
    input.dataset.originalContenteditable = input.contentEditable || 'inherit';

    if (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA') {
      input.readOnly = true;
    } else if (input.contentEditable) {
      input.contentEditable = 'false';
    }
  });

  // Add click handlers to show message
  inputs.forEach(input => {
    input.addEventListener('click', showFocusSafeMessage);
  });

  appLog("Focus-Safe Mode enabled - inputs are read-only");
}

function disableFocusSafeMode() {
  // Restore input fields
  const inputs = document.querySelectorAll('input, textarea, [contenteditable]');
  inputs.forEach(input => {
    const originalReadonly = input.dataset.originalReadonly;
    const originalContenteditable = input.dataset.originalContenteditable;

    if (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA') {
      input.readOnly = originalReadonly === 'true';
    } else if (originalContenteditable) {
      input.contentEditable = originalContenteditable;
    }

    input.removeEventListener('click', showFocusSafeMessage);
    delete input.dataset.originalReadonly;
    delete input.dataset.originalContenteditable;
  });

  appLog("Focus-Safe Mode disabled - inputs are interactive");
}

function showFocusSafeMessage(event) {
  event.preventDefault();

  // Show temporary message
  const message = document.createElement('div');
  message.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 152, 0, 0.95);
    color: white;
    padding: 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    z-index: 10001;
    text-align: center;
    box-shadow: 0 4px 16px rgba(0,0,0,0.3);
  `;
  message.innerHTML = `
    🛡️ Focus-Safe Mode is ON<br>
    <small>Click the indicator or press Ctrl+Shift+F to enable typing</small>
  `;

  document.body.appendChild(message);

  setTimeout(() => {
    message.remove();
  }, 2000);
}

function loadOnScreenKeyboard() {
  appLog("Loading on-screen keyboard...");

  const script = document.createElement('script');
  script.src = 'onscreen-keyboard.js';
  script.onload = () => {
    appLog("✅ On-screen keyboard loaded successfully");
  };
  script.onerror = () => {
    appLog("❌ Failed to load on-screen keyboard");
  };

  document.head.appendChild(script);
}

// Initialize app
function initApp() {
  appLog("Initializing app");

  // Initialize Focus-Safe Mode
  initFocusSafeMode();

  // Load on-screen keyboard
  loadOnScreenKeyboard();

  // Synchronize credentials between user and helperCredentials
  try {
    let credentials = JSON.parse(localStorage.getItem('helperCredentials'));
    if (!credentials || !credentials.email || !credentials.secretKey) {
      const user = JSON.parse(localStorage.getItem('user'));
      if (user && user.email && user.secretKey) {
        credentials = {
          email: user.email,
          secretKey: user.secretKey
        };
        // Store these credentials for future use
        localStorage.setItem('helperCredentials', JSON.stringify(credentials));
        appLog("Initialization: Retrieved credentials from user object and stored in helperCredentials");
      } else {
        appLog("Warning: No valid credentials found in either helperCredentials or user storage");
      }
    } else {
      appLog('Credentials retrieved in helper:', credentials);
    }
  } catch (error) {
    appLog(`Error synchronizing credentials: ${error.message}`);
  }

  // Use ui.js tab navigation setup
  if (window.ui && window.ui.setupTabNavigation) {
    window.ui.setupTabNavigation();
  } else {
    appLog("Error: window.ui.setupTabNavigation not available");
  }

  // Use ui.js dropdown setup instead of our own
  if (window.ui && window.ui.setupDropdowns) {
    window.ui.setupDropdowns();
    appLog("Using ui.js dropdown setup");
  } else {
    appLog("Error: window.ui.setupDropdowns not available, falling back to local implementation");
    initializeCustomDropdowns();
  }

  setupEventListeners();

  if (window.ui && window.ui.populateShortcutsHeader) {
    window.ui.populateShortcutsHeader();
  } else {
    appLog("Error: window.ui.populateShortcutsHeader not available");
  }

  if (window.ui && window.ui.populateShortcutsHelp) {
    window.ui.populateShortcutsHelp();
  } else {
    appLog("Error: window.ui.populateShortcutsHelp not available");
  }
}

// Initialize after DOM is fully loaded
document.addEventListener('DOMContentLoaded', async () => {
  appLog("App script loaded and DOM is ready");

  if (typeof window.electronAPI === 'undefined') {
    appLog("Error: window.electronAPI is not defined. Check preload.js and main.js");
  } else {
    appLog("window.electronAPI is available");
  }

  initApp();

  if (window.electronAPI) {
    try {
      const position = await window.electronAPI.getWindowPosition();
      if (position && typeof position.x === 'number' && typeof position.y === 'number') {
        windowPosition = position;
      }
      const size = await window.electronAPI.getWindowSize();
      if (size && typeof size.width === 'number' && typeof size.height === 'number') {
        windowSize = size;
        if (window.ui && window.ui.updateSizeDisplay) {
          window.ui.updateSizeDisplay(windowSize.width, windowSize.height);
        }
      }
    } catch (error) {
      console.error("Error initializing window properties:", error);
    }
  }
});