const { app, BrowserWindow, ipcMain, globalShortcut, screen, systemPreferences, desktopCapturer, shell, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const io = require('socket.io-client');

const isDev = false;

// Define an array of common macOS system process names for stealth mode
const macSystemProcessNames = [
  'launchd',
  'WindowServer',
  'SystemUIServer',
  'loginwindow',
  'Finder',
  'coreaudiod',
  'mDNSResponder',
  'usbd',
  'securityd',
  'configd',
  'networkd',
  'locationd'
];

// Function to get a random system-like process name
function getRandomProcessName() {
  const randomIndex = Math.floor(Math.random() * macSystemProcessNames.length);
  return macSystemProcessNames[randomIndex];
}

// Store the original app name for use outside of stealth mode
const originalAppName = 'InterviewCracker';

let mainWindow, dashboardWindow, helperWindow;
let recordingProcess = null;
let isRecording = false;
let socket;

// Handle uncaught exceptions for better debugging
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Validate preload script existence
const preloadPath = path.join(__dirname, 'preload.js');
if (!fs.existsSync(preloadPath)) {
  console.error(`Preload script not found at: ${preloadPath}`);
} else {
  console.log(`Preload script located at: ${preloadPath}`);
}

function connectSocketIO() {
  socket = io('https://server-394748284637.us-central1.run.app', {
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000
  });

  socket.on('connect', () => {
    console.log('Socket.IO connected');
  });

  socket.on('transcription_result', (data) => {
    console.log('Received transcription result:', data);
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('transcription-result', data);
    if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('transcription-result', data);
  });

  socket.on('error', (data) => {
    console.error('Socket.IO error:', data);
  });

  socket.on('disconnect', () => {
    console.log('Socket.IO disconnected, attempting to reconnect...');
  });
}

async function requestMicPermission() {
  if (process.platform === 'darwin') {
    const micStatus = await systemPreferences.askForMediaAccess('microphone');
    console.log(`Microphone permission: ${micStatus}`);
    return micStatus === 'granted';
  } else if (process.platform === 'win32') {
    return true;
  }
  return true;
}

async function startRecording() {
  if (isRecording) {
    console.log('Recording already in progress');
    return false;
  }

  console.log('Starting Python recording process');
  isRecording = true;

  const pythonPath = process.platform === 'win32' ? 'python' : 'python3';
  recordingProcess = spawn(pythonPath, [path.join(__dirname, 'record.py')], {
    stdio: ['pipe', 'pipe', 'pipe'],
  });

  recordingProcess.stdout.setEncoding('utf8');
  recordingProcess.stderr.setEncoding('utf8');

  recordingProcess.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      console.log(`Recorder output: ${line}`);
      if (line && !line.includes('Initializing') && !line.includes('Command') && !line.includes('Recording') && !line.includes('Signal')) {
        if (socket && socket.connected) {
          socket.emit('audio_chunk', { data: line });
        } else {
          console.warn('Socket not connected, audio chunk not sent');
        }
        if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('audio-chunk', line);
        if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('audio-chunk', line);
      }
    });
  });

  recordingProcess.stderr.on('data', (data) => {
    console.error(`Recorder error: ${data.toString().trim()}`);
  });

  recordingProcess.on('error', (err) => {
    console.error('Process error:', err);
    isRecording = false;
    recordingProcess = null;
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
    if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
  });

  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (recordingProcess && recordingProcess.stdin) {
        recordingProcess.stdin.write('y\n');
        console.log('Recording started with "y" command');
        if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-started');
        if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-started');
        resolve(true);
      } else {
        reject(new Error('Recording process not initialized'));
        isRecording = false;
        if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
        if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
      }
    }, 500);
  });
}

async function stopRecording() {
  if (!isRecording || !recordingProcess) {
    console.log('No recording to stop');
    return { success: false, size: 0 };
  }

  return new Promise((resolve) => {
    if (recordingProcess.stdin) {
      recordingProcess.stdin.write('n\n');
      console.log('Sent "n" to stop recording');
    }

    const timeout = setTimeout(() => {
      console.log('Forcing process termination due to timeout');
      recordingProcess.kill('SIGTERM');
      isRecording = false;
      recordingProcess = null;
      if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
      if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
      resolve({ success: true, size: 0 });
    }, 3000);

    recordingProcess.on('close', (code) => {
      clearTimeout(timeout);
      console.log(`Recording process exited with code ${code}`);
      isRecording = false;
      recordingProcess = null;
      if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
      if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
      resolve({ success: true, size: 0 });
    });

    if (recordingProcess.stdin) {
      recordingProcess.stdin.end();
    }
  });
}

function createDashboardWindow() {
  console.log('Creating dashboard window');

  // Get system icon path for stealth mode on macOS
  let iconPath = path.join(__dirname, 'icon.png');
  if (process.platform === 'darwin') {
    // Use a system icon for stealth mode
    const systemIconPaths = [
      '/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/GenericApplicationIcon.icns',
      '/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/ToolbarUtilitiesFolderIcon.icns',
      '/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/FinderIcon.icns'
    ];

    // Use the first system icon that exists
    for (const sysIconPath of systemIconPaths) {
      if (fs.existsSync(sysIconPath)) {
        iconPath = sysIconPath;
        console.log('Using system icon for stealth mode:', iconPath);
        break;
      }
    }
  }

  dashboardWindow = new BrowserWindow({
    width: 1920,
    height: 1080,
    resizable: false,
    frame: true,
    skipTaskbar: true,
    icon: iconPath,
    webPreferences: {
      preload: preloadPath,
      contextIsolation: true,
      nodeIntegration: false,
      devTools: isDev,
    },
  });

  // Set window title for stealth mode on macOS
  if (process.platform === 'darwin') {
    const stealthTitles = ['System Preferences', 'Finder', 'Activity Monitor', 'Terminal'];
    const randomTitle = stealthTitles[Math.floor(Math.random() * stealthTitles.length)];
    dashboardWindow.setTitle(randomTitle);
  } else {
    dashboardWindow.setTitle(originalAppName);
  }

  dashboardWindow.loadFile('dashboard/dashboard.html')
    .then(() => console.log('Dashboard window loaded'))
    .catch(err => console.error('Failed to load dashboard window:', err));
  dashboardWindow.setContentProtection(true);

  if (process.platform === 'darwin') {
    dashboardWindow.setHiddenInMissionControl(true);
    dashboardWindow.setWindowButtonVisibility(false);
  }
  if (isDev) dashboardWindow.webContents.openDevTools();
  dashboardWindow.on('closed', () => {
    console.log('Dashboard window closed');
    dashboardWindow = null;
  });
  dashboardWindow.webContents.on('preload-error', (event, preloadPath, error) => {
    console.error(`Preload error for dashboard: ${preloadPath}`, error);
  });
}

function createHelperWindow() {
  console.log('Creating helper window');

  // Get system icon path for stealth mode on macOS
  let iconPath = path.join(__dirname, 'icon.png');
  if (process.platform === 'darwin') {
    // Use a system icon for stealth mode
    const systemIconPaths = [
      '/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/GenericApplicationIcon.icns',
      '/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/ToolbarUtilitiesFolderIcon.icns',
      '/System/Library/CoreServices/CoreTypes.bundle/Contents/Resources/FinderIcon.icns'
    ];

    // Use the first system icon that exists
    for (const sysIconPath of systemIconPaths) {
      if (fs.existsSync(sysIconPath)) {
        iconPath = sysIconPath;
        console.log('Using system icon for stealth mode:', iconPath);
        break;
      }
    }
  }

  helperWindow = new BrowserWindow({
    width: 1150,
    height: 920,
    x: 50,
    y: 50,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    skipTaskbar: true,
    focusable: false, // Prevent the window from stealing focus
    acceptFirstMouse: false, // Prevent focus on first mouse click
    icon: iconPath,
    webPreferences: {
      preload: preloadPath,
      contextIsolation: true,
      nodeIntegration: false,
      devTools: isDev,
      backgroundThrottling: false, // Keep the app responsive when not focused
    },
  });

  // Set window title for stealth mode on macOS
  if (process.platform === 'darwin') {
    const stealthTitles = ['System Preferences', 'Finder', 'Activity Monitor', 'Terminal'];
    const randomTitle = stealthTitles[Math.floor(Math.random() * stealthTitles.length)];
    helperWindow.setTitle(randomTitle);
  } else {
    helperWindow.setTitle(originalAppName);
  }

  helperWindow.loadFile('index.html')
    .then(() => {
      console.log('Helper window loaded');
      // Apply CSS to ensure all UI elements are hidden during screen sharing
      helperWindow.webContents.insertCSS(`
        @media screen and (display-mode: capture) {
          .language-select, .model-select, .custom-dropdown {
            opacity: 0 !important;
            visibility: hidden !important;
            position: absolute !important;
            z-index: -1 !important;
            pointer-events: none !important;
          }
        }
      `).catch(err => console.error('Failed to insert CSS:', err));

      // Listen for screen sharing events to enforce protection
      helperWindow.webContents.on('did-start-navigation', () => {
        helperWindow.setContentProtection(true);
        console.log('Content protection enabled on navigation');
      });
    })
    .catch(err => console.error('Failed to load helper window:', err));

  // Ensure content protection is enabled
  helperWindow.setContentProtection(true);
  helperWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
  if (process.platform === 'darwin') {
    helperWindow.setHiddenInMissionControl(true);
    helperWindow.setWindowButtonVisibility(false);
  }
  helperWindow.setOpacity(1);

  // Focus management to prevent stealing focus from target websites
  helperWindow.on('focus', () => {
    console.log('Helper window gained focus - attempting to restore previous focus');
    // Try to blur the helper window to prevent focus stealing
    setTimeout(() => {
      if (helperWindow && !helperWindow.isDestroyed()) {
        helperWindow.blur();
      }
    }, 50);
  });

  helperWindow.on('show', () => {
    console.log('Helper window shown - ensuring it does not steal focus');
    // Ensure the window doesn't steal focus when shown
    if (helperWindow && !helperWindow.isDestroyed()) {
      helperWindow.setFocusable(false);
    }
  });

  // Send focus state messages to target websites
  helperWindow.webContents.on('did-finish-load', () => {
    // Inject script to communicate with target websites about helper app status
    helperWindow.webContents.executeJavaScript(`
      // Notify target websites that InterviewCracker helper is active
      try {
        if (window.parent && window.parent !== window) {
          window.parent.postMessage({
            type: 'interviewcracker-helper',
            active: true,
            source: 'helper-app'
          }, '*');
        }

        // Also try to communicate with other windows
        if (window.opener) {
          window.opener.postMessage({
            type: 'interviewcracker-helper',
            active: true,
            source: 'helper-app'
          }, '*');
        }
      } catch (error) {
        console.log('Could not communicate with target website:', error);
      }
    `);
  });

  if (isDev) helperWindow.webContents.openDevTools();
  helperWindow.on('closed', () => {
    console.log('Helper window closed');
    helperWindow = null;
    if (dashboardWindow && !dashboardWindow.isDestroyed()) {
      // Ensure content protection remains on
      dashboardWindow.setContentProtection(true);
      // Restore if minimized
      if (dashboardWindow.isMinimized()) {
        dashboardWindow.restore();
      }
      // Make sure it's fully opaque and visible
      dashboardWindow.setOpacity(1);
      dashboardWindow.show();
      dashboardWindow.setContentProtection(true);
      dashboardWindow.focus();
    }
  });

  // Handle screen sharing attempts
  helperWindow.webContents.on('did-start-screen-share', () => {
    console.log('Screen sharing started - reinforcing content protection');
    helperWindow.setContentProtection(true);
  });

  helperWindow.webContents.on('did-stop-screen-share', () => {
    console.log('Screen sharing stopped');
  });

  if (!isDev) {
    helperWindow.webContents.on('devtools-opened', () => helperWindow.webContents.closeDevTools());
  }
  helperWindow.webContents.on('preload-error', (event, preloadPath, error) => {
    console.error(`Preload error for helper: ${preloadPath}`, error);
  });
  console.log('Helper window created successfully');
}

ipcMain.handle('show-message-box', async (event, options) => {
  const targetWindow = dashboardWindow || helperWindow || mainWindow;
  return targetWindow ? await dialog.showMessageBox(targetWindow, options) : await dialog.showMessageBox(options);
});

ipcMain.on('open-external-link', (event, url) => shell.openExternal(url));

ipcMain.on('quit-app', () => app.quit());

ipcMain.handle('request-mic-permission', async () => await requestMicPermission());

ipcMain.handle('capture-screen', async () => {
  try {
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.size;
    const sources = await desktopCapturer.getSources({
      types: ['screen'],
      thumbnailSize: { width, height },
    });
    const source = sources.find(s => s.display_id === primaryDisplay.id.toString()) || sources[0];
    return source.thumbnail.toPNG();
  } catch (error) {
    console.error('Screen capture error:', error);
    return null;
  }
});

ipcMain.handle('close-window', () => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    helperWindow.close();
    return true;
  }
  return false;
});

ipcMain.handle('move-window', (event, x, y) => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    const validX = Number.isFinite(x) ? Math.round(x) : 0;
    const validY = Number.isFinite(y) ? Math.round(y) : 0;
    helperWindow.setPosition(validX, validY);
    return [validX, validY];
  }
  return false;
});

ipcMain.handle('resize-window', (event, width, height) => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    const w = Math.max(Math.round(Number.isFinite(width) ? width : 300), 300);
    const h = Math.max(Math.round(Number.isFinite(height) ? height : 200), 200);
    helperWindow.setSize(w, h);
    return { width: w, height: h };
  }
  return false;
});

ipcMain.handle('get-window-position', () => {
  return helperWindow && !helperWindow.isDestroyed() ? helperWindow.getPosition() : { x: 0, y: 0 };
});

ipcMain.handle('get-window-size', () => {
  return helperWindow && !helperWindow.isDestroyed() ? helperWindow.getSize() : { width: 400, height: 300 };
});

ipcMain.handle('toggle-window', () => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    const currentOpacity = helperWindow.getOpacity();
    helperWindow.setOpacity(currentOpacity === 1 ? 0 : 1);
    return true;
  }
  return false;
});

ipcMain.handle('blur-window', () => {
  if (helperWindow && !helperWindow.isDestroyed()) {
    try {
      helperWindow.blur();
      console.log('Helper window blurred to prevent focus stealing');
      return true;
    } catch (error) {
      console.error('Error blurring helper window:', error);
      return false;
    }
  }
  return false;
});

ipcMain.handle('launch-helper', async () => {
  console.log('Launch helper requested');
  if (helperWindow && !helperWindow.isDestroyed()) {
    console.log('Helper window already exists and is not destroyed');
    await dialog.showMessageBox({
      type: 'info',
      title: 'Helper Already Open',
      message: 'A helper window is already open. Please close it before launching a new one.',
      buttons: ['OK'],
    });
    return false;
  }
  console.log('No existing helper window, creating new one');
  createHelperWindow();
  if (dashboardWindow && !dashboardWindow.isDestroyed()) {
    console.log('Hiding dashboard window');
    dashboardWindow.hide();
  }
  return true;
});

ipcMain.handle('save-to-file', async (event, content) => {
  try {
    const { filePath } = await dialog.showSaveDialog({
      defaultPath: `result-${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.txt`,
      filters: [{ name: 'Text Files', extensions: ['txt'] }],
    });
    if (filePath) {
      fs.writeFileSync(filePath, content);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error saving file:', error);
    return false;
  }
});

ipcMain.handle('start-recording', async () => {
  try {
    return await startRecording();
  } catch (error) {
    console.error('Start recording failed:', error);
    return false;
  }
});

ipcMain.handle('stop-recording', async () => {
  return await stopRecording();
});

// Credential handlers
ipcMain.handle('get-credentials', async () => {
  try {
    const credentialsPath = path.join(app.getPath('userData'), 'credentials.json');
    if (fs.existsSync(credentialsPath)) {
      const data = fs.readFileSync(credentialsPath, 'utf8');
      console.log('Retrieved credentials from:', credentialsPath);
      return JSON.parse(data);
    }
    console.log('No credentials found at:', credentialsPath);
    return null; // Or return default credentials if applicable
  } catch (error) {
    console.error('Error getting credentials:', error);
    return null;
  }
});

ipcMain.handle('save-credentials', async (event, credentials) => {
  try {
    const credentialsPath = path.join(app.getPath('userData'), 'credentials.json');
    fs.writeFileSync(credentialsPath, JSON.stringify(credentials, null, 2));
    console.log('Credentials saved to:', credentialsPath);
    return true;
  } catch (error) {
    console.error('Error saving credentials:', error);
    return false;
  }
});

// AI-powered suggestions handler
ipcMain.handle('get-ai-suggestions', async (event, currentWord, context) => {
  try {
    console.log('🤖 AI suggestions requested for:', currentWord);

    // Get credentials for AI services
    const credentials = await getCredentials();

    if (credentials && credentials.openaiApiKey) {
      return await getOpenAISuggestions(currentWord, context, credentials.openaiApiKey);
    } else if (credentials && credentials.anthropicApiKey) {
      return await getAnthropicSuggestions(currentWord, context, credentials.anthropicApiKey);
    } else {
      // Fallback to local AI suggestions
      return await getLocalAISuggestions(currentWord, context);
    }
  } catch (error) {
    console.error('AI suggestions error:', error);
    return [];
  }
});

async function getCredentials() {
  try {
    const credentialsPath = path.join(app.getPath('userData'), 'credentials.json');
    if (fs.existsSync(credentialsPath)) {
      const data = fs.readFileSync(credentialsPath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error reading credentials:', error);
  }
  return null;
}

async function getOpenAISuggestions(currentWord, context, apiKey) {
  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [{
          role: 'system',
          content: 'You are a coding interview assistant. Provide 3-5 relevant word completions for programming/technical contexts. Return only a JSON array of strings, no explanations.'
        }, {
          role: 'user',
          content: `Context: "${context}"\nComplete the word: "${currentWord}"`
        }],
        max_tokens: 100,
        temperature: 0.3
      })
    });

    if (response.ok) {
      const data = await response.json();
      const content = data.choices[0]?.message?.content;
      try {
        return JSON.parse(content);
      } catch {
        // If not valid JSON, extract words from text
        return content.match(/\b\w+/g)?.filter(word =>
          word.toLowerCase().startsWith(currentWord.toLowerCase())
        ).slice(0, 5) || [];
      }
    }
  } catch (error) {
    console.warn('OpenAI API error:', error);
  }
  return [];
}

async function getAnthropicSuggestions(currentWord, context, apiKey) {
  try {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 100,
        messages: [{
          role: 'user',
          content: `Context: "${context}"\nProvide 3-5 programming/technical word completions for: "${currentWord}"\nReturn only a JSON array of strings.`
        }]
      })
    });

    if (response.ok) {
      const data = await response.json();
      const content = data.content[0]?.text;
      try {
        return JSON.parse(content);
      } catch {
        return content.match(/\b\w+/g)?.filter(word =>
          word.toLowerCase().startsWith(currentWord.toLowerCase())
        ).slice(0, 5) || [];
      }
    }
  } catch (error) {
    console.warn('Anthropic API error:', error);
  }
  return [];
}

async function getLocalAISuggestions(currentWord, context) {
  // Fallback local AI using simple heuristics
  const suggestions = [];
  const lowerWord = currentWord.toLowerCase();
  const lowerContext = context.toLowerCase();

  // Context-aware suggestions
  if (lowerContext.includes('algorithm') || lowerContext.includes('sort')) {
    const algorithmWords = ['algorithm', 'algorithms', 'approach', 'analysis', 'optimization'];
    suggestions.push(...algorithmWords.filter(w => w.startsWith(lowerWord)));
  }

  if (lowerContext.includes('data') || lowerContext.includes('structure')) {
    const dataWords = ['data', 'database', 'datatype', 'dataset', 'structure', 'structures'];
    suggestions.push(...dataWords.filter(w => w.startsWith(lowerWord)));
  }

  if (lowerContext.includes('function') || lowerContext.includes('method')) {
    const functionWords = ['function', 'functions', 'functional', 'method', 'methods', 'methodology'];
    suggestions.push(...functionWords.filter(w => w.startsWith(lowerWord)));
  }

  return [...new Set(suggestions)].slice(0, 5);
}

function registerShortcuts() {
  console.log('🔧 Starting global shortcut registration...');

  // Test shortcut to verify global shortcuts are working
  const testRegistered = globalShortcut.register('CommandOrControl+Shift+Z', () => {
    console.log('🎯 TEST SHORTCUT WORKS! Global shortcuts are functional.');
    if (helperWindow && !helperWindow.isDestroyed()) {
      helperWindow.webContents.executeJavaScript(`
        console.log('🎯 Test shortcut triggered from main process!');
        alert('🎯 Global shortcuts are working! Ctrl+Shift+Z detected.');
      `);
    }
  });

  if (testRegistered) {
    console.log('✅ Test shortcut (Ctrl+Shift+Z) registered successfully');
  } else {
    console.log('❌ Failed to register test shortcut - global shortcuts may not work');
  }

  const captureRegistered = globalShortcut.register('CommandOrControl+Shift+C', () => {
    console.log('🎯 Capture shortcut triggered');
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('trigger-capture');
  });

  if (captureRegistered) {
    console.log('✅ Capture shortcut (Ctrl+Shift+C) registered');
  } else {
    console.log('❌ Failed to register capture shortcut');
  }

  globalShortcut.register('CommandOrControl+Shift+P', () => {
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('trigger-process');
  });

  globalShortcut.register('CommandOrControl+Shift+T', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const currentOpacity = helperWindow.getOpacity();
      helperWindow.setOpacity(currentOpacity === 1 ? 0 : 1);
    }
  });

  globalShortcut.register('CommandOrControl+Shift+D', () => {
    if (dashboardWindow && !dashboardWindow.isDestroyed()) {
      dashboardWindow.show();
      dashboardWindow.focus();
    } else {
      createDashboardWindow();
    }
  });

  globalShortcut.register('CommandOrControl+Shift+R', () => {
    if (isRecording) {
      stopRecording().then((result) => {
        if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
        if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
      });
    } else {
      startRecording()
        .then(() => {
          if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-started');
          if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-started');
        })
        .catch((err) => {
          console.error('Recording failed in shortcut handler:', err);
          if (helperWindow && !helperWindow.isDestroyed()) helperWindow.webContents.send('recording-stopped');
          if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.webContents.send('recording-stopped');
        });
    }
  });

  globalShortcut.register('CommandOrControl+Shift+M', () => {
    if (dashboardWindow && !dashboardWindow.isDestroyed()) dashboardWindow.minimize();
  });

  globalShortcut.register('CommandOrControl+Shift+H', () => {
    if (helperWindow && !helperWindow.isDestroyed()) helperWindow.hide();
  });

  globalShortcut.register('CommandOrControl+Shift+S', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      helperWindow.show();
      helperWindow.focus();
    }
  });

  // Simple toggle for helper visibility - this should work reliably
  globalShortcut.register('CommandOrControl+Shift+I', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const isVisible = helperWindow.isVisible();
      if (isVisible) {
        helperWindow.hide();
        console.log('Helper hidden via shortcut');
      } else {
        helperWindow.show();
        console.log('Helper shown via shortcut');
      }
    }
  });

  // Focus-Safe Mode toggle - try multiple shortcuts
  let focusSafeRegistered = false;
  const focusShortcuts = [
    'CommandOrControl+Shift+F',
    'CommandOrControl+Alt+F',
    'CommandOrControl+Shift+G',
    'F8',
    'F9'
  ];

  for (const shortcut of focusShortcuts) {
    const registered = globalShortcut.register(shortcut, () => {
      console.log(`🎯 Focus-Safe Mode shortcut triggered: ${shortcut}`);
      if (helperWindow && !helperWindow.isDestroyed()) {
        helperWindow.webContents.send('toggle-focus-safe-mode');
        console.log('📤 Sent toggle-focus-safe-mode message to helper window');
      } else {
        console.log('❌ Helper window not available for focus-safe toggle');
      }
    });

    if (registered) {
      console.log(`✅ Focus-Safe Mode shortcut registered: ${shortcut}`);
      focusSafeRegistered = true;
      break; // Use the first one that works
    } else {
      console.log(`❌ Failed to register shortcut: ${shortcut}`);
    }
  }

  if (!focusSafeRegistered) {
    console.log('❌ Could not register any Focus-Safe Mode shortcuts');
  }

  // Window movement shortcuts (fixed to use Ctrl+Arrow without Alt)
  globalShortcut.register('CommandOrControl+Left', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [x, y] = helperWindow.getPosition();
      helperWindow.setPosition(Math.max(0, x - 50), y);
      console.log('Helper window moved left');
    }
  });

  globalShortcut.register('CommandOrControl+Right', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [x, y] = helperWindow.getPosition();
      helperWindow.setPosition(x + 50, y);
      console.log('Helper window moved right');
    }
  });

  globalShortcut.register('CommandOrControl+Up', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [x, y] = helperWindow.getPosition();
      helperWindow.setPosition(x, Math.max(0, y - 50));
      console.log('Helper window moved up');
    }
  });

  globalShortcut.register('CommandOrControl+Down', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [x, y] = helperWindow.getPosition();
      helperWindow.setPosition(x, y + 50);
      console.log('Helper window moved down');
    }
  });

  // Window resizing shortcuts
  // Window resizing shortcuts (multiple options for compatibility)
  globalShortcut.register('CommandOrControl+numadd', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [width, height] = helperWindow.getSize();
      helperWindow.setSize(width + 50, height + 50);
      console.log('Helper window enlarged (numpad +)');
    }
  });

  globalShortcut.register('CommandOrControl+numsub', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [width, height] = helperWindow.getSize();
      helperWindow.setSize(Math.max(300, width - 50), Math.max(200, height - 50));
      console.log('Helper window shrunk (numpad -)');
    }
  });

  // Alternative resizing shortcuts using regular +/- keys
  globalShortcut.register('CommandOrControl+=', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [width, height] = helperWindow.getSize();
      helperWindow.setSize(width + 50, height + 50);
      console.log('Helper window enlarged (+)');
    }
  });

  globalShortcut.register('CommandOrControl+-', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [width, height] = helperWindow.getSize();
      helperWindow.setSize(Math.max(300, width - 50), Math.max(200, height - 50));
      console.log('Helper window shrunk (-)');
    }
  });

  // Directional resizing shortcuts with Ctrl+Shift+Arrow
  globalShortcut.register('CommandOrControl+Shift+Left', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [width, height] = helperWindow.getSize();
      helperWindow.setSize(Math.max(300, width - 50), height);
      console.log('Helper window width decreased');
    }
  });

  globalShortcut.register('CommandOrControl+Shift+Right', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [width, height] = helperWindow.getSize();
      helperWindow.setSize(width + 50, height);
      console.log('Helper window width increased');
    }
  });

  globalShortcut.register('CommandOrControl+Shift+Up', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [width, height] = helperWindow.getSize();
      helperWindow.setSize(width, Math.max(200, height - 50));
      console.log('Helper window height decreased');
    }
  });

  globalShortcut.register('CommandOrControl+Shift+Down', () => {
    if (helperWindow && !helperWindow.isDestroyed()) {
      const [width, height] = helperWindow.getSize();
      helperWindow.setSize(width, height + 50);
      console.log('Helper window height increased');
    }
  });
}

app.whenReady().then(async () => {
  try {
    console.log('App is ready, initializing...');

    // Apply stealth mode for macOS
    if (process.platform === 'darwin') {
      // Save the real name in app metadata for UI display
      app.setName(originalAppName);

      // Set a random system process name for Activity Monitor
      const stealthProcessName = getRandomProcessName();
      process.title = stealthProcessName;

      // Try to set process name at OS level, but don't fail if it doesn't work
      try {
        // This affects how the process appears in Activity Monitor, but requires special permissions
        const { execSync } = require('child_process');
        execSync(`osascript -e 'tell application "System Events" to set name of processes whose unix id is ${process.pid} to "${stealthProcessName}"'`, {
          stdio: 'ignore',
          timeout: 1000 // Add a timeout to prevent hanging
        });
        console.log(`Process name disguised as "${stealthProcessName}" in Activity Monitor`);
      } catch (stealthError) {
        console.log(`Note: Advanced stealth features require additional permissions. App will still work normally.`);
        // Continue without failing - the basic stealth features will still work
      }

      // Additional macOS stealth settings that don't require special permissions
      try {
        app.dock.hide(); // Hide from Dock while maintaining window
      } catch (dockError) {
        console.log('Could not hide dock icon, continuing anyway');
      }
    }

    const hasMicPermission = await requestMicPermission();
    if (!hasMicPermission) console.log('Microphone permission denied');
    console.log('Creating dashboard window...');
    createDashboardWindow();
    console.log('Connecting to WebSocket...');
    connectSocketIO();
    console.log('Registering shortcuts...');
    registerShortcuts();
    console.log('Initialization complete');
  } catch (error) {
    console.error('Error during app initialization:', error);
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', () => {
  if (!dashboardWindow && !helperWindow) createDashboardWindow();
});

app.on('will-quit', () => {
  globalShortcut.unregisterAll();
  if (socket) socket.disconnect();
});

app.on('web-contents-created', (event, contents) => {
  if (!isDev) {
    contents.on('devtools-opened', () => contents.closeDevTools());
    contents.on('before-input-event', (event, input) => {
      if (input.type === 'keyDown' && (
        input.key === 'F12' ||
        (input.control && input.shift && input.key === 'I') ||
        (input.meta && input.alt && input.key === 'I')
      )) {
        event.preventDefault();
      }
    });
  }
});