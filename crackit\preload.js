// preload.js

const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Add debounce utility to prevent multiple rapid calls
function debounce(func, wait) {
  let timeout;
  return function(...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
}

// Track if events are being processed to prevent duplicates
const eventTracker = {
  captureInProgress: false,
  processInProgress: false,
  lastCaptureTime: 0,
  lastProcessTime: 0
};

contextBridge.exposeInMainWorld('electronAPI', {
  requestMicPermission: () => ipcRenderer.invoke('request-mic-permission'),
  captureScreen: () => ipcRenderer.invoke('capture-screen'),
  performOCR: async (imageBuffer) => {
    try {
      const user = JSON.parse(window.localStorage.getItem('helperCredentials') || '{}');
      if (!user || !user.email || !user.secretKey) {
        console.error('[Preload] Missing credentials in localStorage for OCR');
        return 'Missing credentials. Please sign in again.';
      }
      console.log('[Preload] Using credentials for OCR:', user.email);

      const response = await fetch(`https://server-394748284637.us-central1.run.app/ocr?email=${encodeURIComponent(user.email)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'image/png',
          'x-secret-key': user.secretKey
        },
        body: imageBuffer,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Preload] OCR request failed:', response.status, errorText);
        throw new Error(`OCR request failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      return result.text || 'OCR failed';
    } catch (error) {
      console.error('[Preload] OCR error:', error.message);
      return `OCR error: ${error.message}`;
    }
  },
  analyzeWithAI: async (text, conversation = [], model = 'gpt-4o-mini', language = null) => {
    try {
      const user = JSON.parse(window.localStorage.getItem('helperCredentials') || '{}');
      if (!user || !user.email || !user.secretKey) {
        console.error('[Preload] Missing credentials in localStorage for AI analysis');
        return 'Missing credentials. Please sign in again.';
      }
      console.log('[Preload] Using credentials for AI analysis:', user.email);
      console.log('[Preload] Conversation history for analysis:', conversation);
      console.log('[Preload] Model:', model, 'Language:', language);

      const response = await fetch(`https://server-394748284637.us-central1.run.app/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-secret-key': user.secretKey
        },
        body: JSON.stringify({
          text,
          conversation,
          model,
          language,
          email: user.email
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Preload] AI analysis request failed:', response.status, errorText);
        throw new Error(`AI analysis request failed: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      return result.response || 'AI analysis failed';
    } catch (error) {
      console.error('[Preload] AI analysis error:', error.message);
      return `AI analysis error: ${error.message}`;
    }
  },
  sendChatMessage: async (conversation, message, model, language) => {
    try {
      const user = JSON.parse(window.localStorage.getItem('helperCredentials') || '{}');
      if (!user || !user.email || !user.secretKey) {
        console.error('[Preload] Missing credentials in localStorage for chat');
        return { error: 'Missing credentials. Please sign in again.' };
      }
      console.log('[Preload] Using credentials for chat:', user.email);
      console.log('[Preload] Sending chat message with model:', model, 'Language:', language);

      const response = await fetch('https://server-394748284637.us-central1.run.app/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-secret-key': user.secretKey,
        },
        body: JSON.stringify({
          conversation: conversation,
          prompt: message,
          model: model,
          language: language,
          email: user.email,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Preload] Chat request failed:', response.status, errorText);
        return { error: `Chat request failed: ${response.status}` };
      }

      const data = await response.json();
      console.log('[Preload] Chat response received:', data);
      return { success: true, response: data.response || 'No response received from server' };
    } catch (error) {
      console.error('[Preload] Chat error:', error.message);
      return { error: error.message };
    }
  },

  openExternalLink: (url) => ipcRenderer.send('open-external-link', url),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  quitApp: () => ipcRenderer.send('quit-app'),
  onAuthCallback: (callback) => ipcRenderer.on('auth-callback', callback),
  onCapture: (callback) => {
    // Create a wrapper that prevents duplicate calls
    const protectedCallback = (...args) => {
      const now = Date.now();
      // Prevent duplicate events within 1500ms and while an operation is in progress
      if (eventTracker.captureInProgress || now - eventTracker.lastCaptureTime < 1500) {
        console.log('[Preload] Capture event debounced');
        return;
      }

      eventTracker.captureInProgress = true;
      eventTracker.lastCaptureTime = now;

      // Execute the callback and reset the flag when done
      Promise.resolve(callback(...args))
        .finally(() => {
          setTimeout(() => {
            eventTracker.captureInProgress = false;
          }, 500);
        });
    };

    return ipcRenderer.on('trigger-capture', protectedCallback);
  },
  onProcess: (callback) => {
    // Create a wrapper that prevents duplicate calls
    const protectedCallback = (...args) => {
      const now = Date.now();
      // Prevent duplicate events within 1500ms and while an operation is in progress
      if (eventTracker.processInProgress || now - eventTracker.lastProcessTime < 1500) {
        console.log('[Preload] Process event debounced');
        return;
      }

      eventTracker.processInProgress = true;
      eventTracker.lastProcessTime = now;

      // Execute the callback and reset the flag when done
      Promise.resolve(callback(...args))
        .finally(() => {
          setTimeout(() => {
            eventTracker.processInProgress = false;
          }, 500);
        });
    };

    return ipcRenderer.on('trigger-process', protectedCallback);
  },
  closeWindow: () => ipcRenderer.invoke('close-window'),
  moveWindow: (x, y) => ipcRenderer.invoke('move-window', x, y),
  resizeWindow: (width, height) => ipcRenderer.invoke('resize-window', width, height),
  getWindowPosition: () => ipcRenderer.invoke('get-window-position'),
  getWindowSize: () => ipcRenderer.invoke('get-window-size'),
  toggleWindow: () => ipcRenderer.invoke('toggle-window'),
  blurWindow: () => ipcRenderer.invoke('blur-window'),
  launchHelper: () => ipcRenderer.invoke('launch-helper'),
  saveToFile: (content) => ipcRenderer.invoke('save-to-file', content),
  // Audio Recording APIs
  getAudioSources: () => ipcRenderer.invoke('get-audio-sources'),
  startRecording: () => ipcRenderer.invoke('start-recording'),
  stopRecording: () => ipcRenderer.invoke('stop-recording'),
  onAudioChunk: (callback) => ipcRenderer.on('audio-chunk', (event, data) => callback(data)),
  onRecordingStarted: (callback) => ipcRenderer.on('recording-started', callback),
  onRecordingStopped: (callback) => ipcRenderer.on('recording-stopped', callback),
  onFocusSafeModeToggle: (callback) => ipcRenderer.on('toggle-focus-safe-mode', callback),

  // AI-powered suggestions
  getAISuggestions: (currentWord, context) => ipcRenderer.invoke('get-ai-suggestions', currentWord, context),

  // Direct trigger methods to enforce debouncing in app.js calls
  triggerCapture: debounce(() => {
    if (!eventTracker.captureInProgress) {
      ipcRenderer.send('trigger-capture');
    }
  }, 1500),
  triggerProcess: debounce(() => {
    if (!eventTracker.processInProgress) {
      ipcRenderer.send('trigger-process');
    }
  }, 1500),
  // Generic IPC event listener
  on: (channel, callback) => ipcRenderer.on(channel, (event, ...args) => callback(event, ...args)),
});

console.log('[Preload] Preload script completed initialization');